aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asttokens==3.0.0
attrs==25.3.0
beautifulsoup4==4.13.3
bs4==0.0.2
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cloudscraper==1.2.71
colorama==0.4.6
comm==0.2.2
coverage==7.8.0
cryptography==44.0.2
dateparser==1.2.0
debugpy==1.8.14
decorator==5.2.1
diskcache==5.6.3
distro==1.9.0
executing==2.2.0
fastjsonschema==2.21.1
filelock==3.18.0
frozenlist==1.5.0
ged4py==0.5.2
google-ai-generativelanguage==0.6.15  # aligned with google-generativeai 0.8.4 dependency
google-api-core==2.24.2
google-api-python-client==2.167.0
google-auth==2.39.0
google-auth-httplib2==0.2.0
google-genai==1.10.0
google-generativeai==0.8.4
googleapis-common-protos==1.70.0
greenlet==3.1.1
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
httpcore==1.0.8
httplib2==0.22.0
httpx==0.28.1
idna==3.10
iniconfig==2.1.0
ipykernel==6.29.5
ipython==9.1.0
ipython_pygments_lexers==1.1.1
jedi==0.19.2
jiter==0.9.0
JsonForm==0.0.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
JsonSir==0.0.2
jupyter_client==8.6.3
jupyter_core==5.7.2
matplotlib-inline==0.1.7
msal==1.32.0
multidict==6.4.3
nbformat==5.10.4
nest-asyncio==1.6.0
numpy==2.2.3
openai==1.74.0
orjson==3.10.17  # Fast JSON library for performance optimization
outcome==1.3.0.post0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pigar==2.1.7
pipdeptree==2.26.0
platformdirs==4.3.7
pluggy==1.5.0
prompt_toolkit==3.0.50
propcache==0.3.1
proto-plus==1.26.1
protobuf>=3.20.3,<6.0.0  # downgraded range: google-ai-generativelanguage 0.6.15 requires protobuf <6
psutil==7.0.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.3
pydantic_core==2.33.1
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.3
PySocks==1.7.1
pytest==8.3.5
pytest-cov==6.1.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
Python-EasyConfig==0.1.7
pytz==2025.2
pywin32==310
PyYAML==6.0.2
pyzmq==26.4.0
rapidfuzz==3.6.1
referencing==0.36.2
regex==2023.12.25
requests==2.32.3
requests-file==2.1.0
requests-toolbelt==1.0.0
Resource==0.2.1
rpds-py==0.24.0
rsa==4.9
selenium==4.31.0
setuptools==78.1.0
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
SQLAlchemy==2.0.40
stack-data==0.6.3
tabulate==0.9.0
tldextract==5.2.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
trio==0.29.0
trio-websocket==0.12.2
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.2
uritemplate==4.1.1
urllib3==2.4.0
wcwidth==0.2.13
webdriver-manager==4.0.2
websocket-client==1.8.0
websockets==15.0.1
wsproto==1.2.0
yarl==1.19.0
keyring==25.6.0
